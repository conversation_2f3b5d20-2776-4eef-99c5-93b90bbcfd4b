import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";

const AssignTarifsSchema = z.object({
  tarifIds: z.array(z.string()),
});

interface Props {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Check if user group exists and user has permission
    const userGroup = await prisma.userGroup.findUnique({
      where: { id: params.id },
    });

    if (!userGroup) {
      return NextResponse.json({ error: "User group not found" }, { status: 404 });
    }

    if (session.user.role !== Role.ADMIN && userGroup.ouId !== session.user.selectedOu.id) {
      return NextResponse.json({ error: "Unauthorized for this OU" }, { status: 403 });
    }

    // Get assigned tarifs
    const assignedTarifs = await prisma.companyTarifOnUserGroup.findMany({
      where: { userGroupId: params.id },
      include: {
        tarif: true,
      },
    });

    // Get available tarifs for this OU (excluding internal tarifs)
    const availableTarifs = await prisma.companyTarif.findMany({
      where: {
        ouId: userGroup.ouId,
        internal: false, // Exclude internal tarifs from user group assignment
      },
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json({
      assigned: assignedTarifs.map((at) => at.tarif),
      available: availableTarifs,
    });
  } catch (error) {
    console.error("Error fetching user group tarifs:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session?.user?.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const result = AssignTarifsSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json({ error: result.error.issues }, { status: 400 });
    }

    // Check if user group exists and user has permission
    const userGroup = await prisma.userGroup.findUnique({
      where: { id: params.id },
    });

    if (!userGroup) {
      return NextResponse.json({ error: "User group not found" }, { status: 404 });
    }

    if (session.user.role !== Role.ADMIN && userGroup.ouId !== session.user.selectedOu.id) {
      return NextResponse.json({ error: "Unauthorized for this OU" }, { status: 403 });
    }

    // Verify all tarifs belong to the same OU as the UserGroup and are not internal
    const tarifs = await prisma.companyTarif.findMany({
      where: {
        id: { in: result.data.tarifIds },
        ouId: userGroup.ouId, // Only tarifs from the same OU as UserGroup
        internal: false, // Exclude internal tarifs from user group assignment
      },
    });

    if (tarifs.length !== result.data.tarifIds.length) {
      return NextResponse.json(
        {
          error:
            "Einige Tarife gehören nicht zu derselben OU wie die Nutzergruppe, sind interne Tarife oder existieren nicht",
        },
        { status: 400 },
      );
    }

    // Validate AC/DC tarif limits - only one valid AC and one valid DC tarif allowed
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const validTarifs = tarifs.filter(
      (tarif) => tarif.validFrom <= today && tarif.validTo >= today,
    );

    const acTarifs = validTarifs.filter((tarif) => tarif.currentType === "AC");
    const dcTarifs = validTarifs.filter((tarif) => tarif.currentType === "DC");

    if (acTarifs.length > 1) {
      return NextResponse.json(
        { error: "Eine Nutzergruppe kann nur einen gültigen AC-Tarif haben" },
        { status: 400 },
      );
    }

    if (dcTarifs.length > 1) {
      return NextResponse.json(
        { error: "Eine Nutzergruppe kann nur einen gültigen DC-Tarif haben" },
        { status: 400 },
      );
    }

    // Use transaction to update tarif assignments
    await prisma.$transaction(async (tx) => {
      // Remove existing assignments
      await tx.companyTarifOnUserGroup.deleteMany({
        where: { userGroupId: params.id },
      });

      // Add new assignments
      if (result.data.tarifIds.length > 0) {
        await tx.companyTarifOnUserGroup.createMany({
          data: result.data.tarifIds.map((tarifId) => ({
            tarifId,
            userGroupId: params.id,
          })),
        });
      }
    });

    // Return updated assignments
    const updatedAssignments = await prisma.companyTarifOnUserGroup.findMany({
      where: { userGroupId: params.id },
      include: {
        tarif: true,
      },
    });

    return NextResponse.json({
      assigned: updatedAssignments.map((ua) => ua.tarif),
      message: "Tarif-Zuordnungen erfolgreich aktualisiert",
    });
  } catch (error) {
    console.error("Error updating user group tarifs:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
