"use client";

import { useState, useEffect } from "react";
import Card from "../../../../component/card";
import Button from "~/component/button";
import { FiLoader } from "react-icons/fi";
import { CompanyTarif } from "@prisma/client";

interface UserGroupTarifsData {
  assigned: CompanyTarif[];
  available: CompanyTarif[];
}

interface Props {
  userGroupId: string;
  userGroupName?: string;
}

const UserGroupTarifs = ({ userGroupId, userGroupName }: Props) => {
  const [data, setData] = useState<UserGroupTarifsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [selectedTarifs, setSelectedTarifs] = useState<string[]>([]);

  useEffect(() => {
    fetchTarifs();
  }, [userGroupId]);

  const fetchTarifs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/userGroup/${userGroupId}/tarifs`);

      if (!response.ok) {
        throw new Error("Fehler beim Laden der Tarife");
      }

      const tarifsData = await response.json();
      setData(tarifsData);
      setSelectedTarifs(tarifsData.assigned.map((t: CompanyTarif) => t.id));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setLoading(false);
    }
  };

  const handleTarifToggle = (tarifId: string) => {
    if (!data) return;

    const tarif = data.available.find((t) => t.id === tarifId);
    if (!tarif) return;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isValidTarif = new Date(tarif.validFrom) <= today && new Date(tarif.validTo) >= today;

    let newSelection: string[];

    if (selectedTarifs.includes(tarifId)) {
      // Deselecting - always allowed
      newSelection = selectedTarifs.filter((id) => id !== tarifId);
      setError(""); // Clear any errors when deselecting
    } else {
      // Selecting - check AC/DC constraints for valid tarifs only
      if (isValidTarif && (tarif.currentType === "AC" || tarif.currentType === "DC")) {
        // Check if there's already a tarif of the same type selected
        const existingTarifOfSameType = selectedTarifs.find((id) => {
          const existingTarif = data.available.find((t) => t.id === id);
          if (!existingTarif) return false;

          const isExistingValid =
            new Date(existingTarif.validFrom) <= today && new Date(existingTarif.validTo) >= today;
          return isExistingValid && existingTarif.currentType === tarif.currentType;
        });

        if (existingTarifOfSameType) {
          // Prevent selection and show error
          const existingTarif = data.available.find((t) => t.id === existingTarifOfSameType);
          setError(
            `Es ist bereits ein ${tarif.currentType}-Tarif ausgewählt: "${existingTarif?.name}". Bitte entfernen Sie diesen zuerst.`,
          );
          return; // Don't update selection
        }

        // No conflict, add the new tarif
        newSelection = [...selectedTarifs, tarifId];
        setError(""); // Clear any previous errors
      } else {
        // For non-AC/DC or invalid tarifs, just add to selection
        newSelection = [...selectedTarifs, tarifId];
        setError(""); // Clear any previous errors
      }
    }

    setSelectedTarifs(newSelection);
  };

  const validateTarifSelection = (tarifIds: string[]) => {
    if (!data) return { valid: true, error: "" };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const selectedTarifObjects = data.available.filter((t) => tarifIds.includes(t.id));
    const validSelectedTarifs = selectedTarifObjects.filter(
      (tarif) => new Date(tarif.validFrom) <= today && new Date(tarif.validTo) >= today,
    );

    const acTarifs = validSelectedTarifs.filter((tarif) => tarif.currentType === "AC");
    const dcTarifs = validSelectedTarifs.filter((tarif) => tarif.currentType === "DC");

    if (acTarifs.length > 1) {
      return { valid: false, error: "Eine Nutzergruppe kann nur einen gültigen AC-Tarif haben" };
    }

    if (dcTarifs.length > 1) {
      return { valid: false, error: "Eine Nutzergruppe kann nur einen gültigen DC-Tarif haben" };
    }

    return { valid: true, error: "" };
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError("");
      setSuccessMessage("");

      // Validate before saving
      const validation = validateTarifSelection(selectedTarifs);
      if (!validation.valid) {
        setError(validation.error);
        return;
      }

      const response = await fetch(`/api/userGroup/${userGroupId}/tarifs`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ tarifIds: selectedTarifs }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Fehler beim Speichern");
      }

      const result = await response.json();
      setSuccessMessage(result.message || "Tarif-Zuordnungen erfolgreich aktualisiert");

      // Refresh data
      await fetchTarifs();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setSaving(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 4,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("de-DE");
  };

  if (loading) {
    return (
      <Card header_left="Tarif-Zuordnungen">
        <div className="flex justify-center py-8">
          <FiLoader className="animate-spin text-2xl" />
        </div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card header_left="Tarif-Zuordnungen">
        <p className="text-red-600">Fehler beim Laden der Tarife</p>
      </Card>
    );
  }

  const hasChanges =
    JSON.stringify(selectedTarifs.sort()) !== JSON.stringify(data.assigned.map((t) => t.id).sort());

  return (
    <Card
      header_left={`Tarifzuordnungen für Nutzergruppe: "${
        userGroupName ? `${userGroupName}` : ""
      }"`}
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Wählen Sie die Tarife aus der gleichen OU aus, die für diese Nutzergruppe verfügbar
              sein sollen.
            </p>
            <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
              💡 Benutzer dieser Nutzergruppe sehen bei der Ladekarten-Registrierung nur die hier zugeordneten Tarife.
            </p>
            <p className="mt-1 text-xs text-amber-600 dark:text-amber-400">
              ⚠️ Pro Nutzergruppe: Maximal 1 gültiger{" "}
              <span className="rounded bg-green-500 px-1 text-white">AC</span>-Tarif und 1 gültiger{" "}
              <span className="rounded bg-purple-500 px-1 text-white">DC</span>-Tarif
            </p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              🚫 Interne Tarife können nicht zu Nutzergruppen zugeordnet werden.
            </p>
          </div>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || saving}
            className="flex items-center"
          >
            {saving && <FiLoader className="mr-2 animate-spin" />}
            Speichern
          </Button>
        </div>

        {successMessage && (
          <div className="rounded-md bg-green-50 p-4">
            <div className="text-sm text-green-700">{successMessage}</div>
          </div>
        )}

        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        <div className="space-y-6">
          {data.available.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">Keine Tarife für diese OU verfügbar.</p>
          ) : (
            <div className="space-y-3">
              {data.available.map((tarif) => (
                <TarifCard
                  key={tarif.id}
                  tarif={tarif}
                  isSelected={selectedTarifs.includes(tarif.id)}
                  onToggle={handleTarifToggle}
                  formatPrice={formatPrice}
                  formatDate={formatDate}
                  selectedTarifs={selectedTarifs}
                  availableTarifs={data.available}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

// Separate TarifCard component for better reusability
const TarifCard = ({
  tarif,
  isSelected,
  onToggle,
  formatPrice,
  formatDate,
  selectedTarifs,
  availableTarifs,
}) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const isValid = new Date(tarif.validFrom) <= today && new Date(tarif.validTo) >= today;

  // Check if this tarif can be selected (for AC/DC constraints)
  const canBeSelected = () => {
    if (isSelected) return true; // Already selected tarifs can always be deselected
    if (
      !isValid ||
      !tarif.currentType ||
      (tarif.currentType !== "AC" && tarif.currentType !== "DC")
    ) {
      return true; // Non-AC/DC or invalid tarifs can always be selected
    }

    // Check if there's already a tarif of the same type selected
    const existingTarifOfSameType = selectedTarifs.find((id) => {
      const existingTarif = availableTarifs.find((t) => t.id === id);
      if (!existingTarif) return false;

      const isExistingValid =
        new Date(existingTarif.validFrom) <= today && new Date(existingTarif.validTo) >= today;
      return isExistingValid && existingTarif.currentType === tarif.currentType;
    });

    return !existingTarifOfSameType;
  };

  const isSelectable = canBeSelected();

  return (
    <div
      className={`rounded-lg border p-4 transition-colors ${
        !isSelectable
          ? "cursor-not-allowed border-gray-200 bg-gray-50 opacity-60 dark:border-gray-700 dark:bg-gray-800"
          : isSelected
          ? "cursor-pointer border-blue-500 bg-blue-50 dark:bg-blue-900/20"
          : "cursor-pointer border-gray-300 hover:border-gray-400 dark:border-gray-600"
      }`}
      onClick={() => isSelectable && onToggle(tarif.id)}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            checked={isSelected}
            disabled={!isSelectable}
            onChange={() => isSelectable && onToggle(tarif.id)}
            className={`mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 ${
              !isSelectable ? "cursor-not-allowed opacity-50" : ""
            }`}
          />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              {/* AC/DC Badge - prominenter anzeigen */}

              {tarif.currentType && (
                <span
                  className={`inline-flex rounded-lg px-3 py-1 text-sm font-bold ${
                    tarif.currentType === "AC"
                      ? "bg-green-500 text-white"
                      : tarif.currentType === "DC"
                      ? "bg-purple-500 text-white"
                      : "bg-gray-500 text-white"
                  }`}
                >
                  {tarif.currentType}
                </span>
              )}
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">{tarif.name}</h4>
              <span
                className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                  isValid
                    ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                    : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                }`}
              >
                {isValid ? "Gültig" : "Ungültig"}
              </span>
              {tarif.optional && (
                <span className="inline-flex rounded-full bg-blue-100 px-2 py-1 text-xs font-semibold text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                  Optional
                </span>
              )}
              {tarif.internal && (
                <span className="inline-flex rounded-full bg-gray-100 px-2 py-1 text-xs font-semibold text-gray-800 dark:bg-gray-800 dark:text-gray-100">
                  Intern
                </span>
              )}
              {!isSelectable &&
                isValid &&
                (tarif.currentType === "AC" || tarif.currentType === "DC") && (
                  <span className="inline-flex rounded-full bg-red-100 px-2 py-1 text-xs font-semibold text-red-800 dark:bg-red-800 dark:text-red-100">
                    Blockiert (bereits ein {tarif.currentType}-Tarif ausgewählt)
                  </span>
                )}
            </div>
            {tarif.description && (
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">{tarif.description}</p>
            )}
            <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Energiepreis:</span>
                <span className="ml-1 text-gray-900 dark:text-white">
                  {formatPrice(tarif.energyPrice)}/kWh
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Sitzungsgebühr:</span>
                <span className="ml-1 text-gray-900 dark:text-white">
                  {formatPrice(tarif.sessionPrice)}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Gültig von:</span>
                <span className="ml-1 text-gray-900 dark:text-white">
                  {formatDate(tarif.validFrom)}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Gültig bis:</span>
                <span className="ml-1 text-gray-900 dark:text-white">
                  {formatDate(tarif.validTo)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserGroupTarifs;
