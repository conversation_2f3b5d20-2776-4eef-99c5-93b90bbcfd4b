"use client";

import React, { useState } from "react";
import { Role } from "@prisma/client";

const TarifCard = ({
  title,
  description = "",
  onCheck,
  pricekWh,
  priceSession,
  tarifName,
  optional = true,
  interactive = true,
  tarifId,
  currentType,
  basicFee,
  oneTimeFee,
  vat = 19,
  blockingFee = 0,
  blockingFeeBeginAtMin = 0,
  blockingFeeMax = 0,
  size = "normal",
  oneTimeFeePayer,
  internal = false,
}: {
  title: string;
  description: string;
  onCheck?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  pricekWh: number;
  priceSession: number;
  tarifName: string;
  optional: boolean;
  interactive: boolean;
  tarifId: string;
  currentType: string;
  basicFee: number;
  oneTimeFee: number;
  oneTimeFeePayer: Role;
  vat: number;
  blockingFee: number;
  blockingFeeBeginAtMin: number;
  blockingFeeMax: number;
  size: "small" | "normal";
  internal: boolean;
}) => {
  const [checked, setChecked] = useState<boolean>(!optional);
  return (
    <div className="mb-6 w-full sm:max-w-75">
      <div
        className={`shadow-md-all flex min-w-0 flex-col break-words rounded-2xl border-0 bg-opacity-20 bg-clip-border `}
      >
        <div className="flex flex-col items-center p-4 ">
          <div className={"flex flex-row items-center gap-1 text-primary"}>
            <h4 className={"font-bold text-primary"}>{tarifName} </h4>
          </div>

          <div className={`flex`}>{description ? description : ""}</div>
          <div className={"flex flex-row items-baseline text-black"}>
            <h4 className="mr-1 font-bold text-black ">
              {(pricekWh * (1 + vat / 100)).toFixed(2).replace(".", ",")}
            </h4>
            <span>€/kWh</span>
          </div>
          <hr className={"my-2 mb-3 h-px w-full border-solid bg-gray-950 dark:bg-gray-700"} />

          <div
            className={`flex flex-col ${
              size == "small" ? "text-sm" : "gap-4"
            } w-full justify-start`}
          >
            {oneTimeFee > 0 && (oneTimeFeePayer == Role.CARD_HOLDER || internal == true) && (
              <div className={"flex flex-row gap-1 text-black text-opacity-50"}>
                <span className={"font-bold"}>Bestellgebühr:</span>
                <span>{(oneTimeFee * (1 + vat / 100)).toFixed(2).replace(".", ",")}€</span>
              </div>
            )}

            {oneTimeFeePayer == Role.CARD_MANAGER && (
              <div className={"flex flex-row gap-1 text-black text-opacity-50"}>
                <span className={"font-bold"}>Bestellgebühr:</span>
                <span>Keine</span>
              </div>
            )}
            {basicFee != 0 && (
              <div className={"flex flex-row gap-1 text-black text-opacity-50"}>
                <span className={"font-bold"}>Grundgebühr:</span>
                <span>{(basicFee * (1 + vat / 100)).toFixed(2).replace(".", ",")}€</span>
              </div>
            )}
            {priceSession != 0 && (
              <div className={"flex flex-row gap-1 text-black text-opacity-50"}>
                <span className={"font-bold"}>Startgebühr:</span>
                <span>{(priceSession * (1 + vat / 100)).toFixed(2).replace(".", ",")}€</span>
              </div>
            )}

            <div className={"flex flex-row gap-1 text-black text-opacity-50"}>
              <span className={"font-bold"}>Preis {currentType}:</span>
              <span>{(pricekWh * (1 + vat / 100)).toFixed(2).replace(".", ",")}€/kWh</span>
            </div>
            {blockingFee != 0 && (
              <div className={"flex flex-row gap-1 text-black text-opacity-50"}>
                <span className={"font-bold"}>Blockiergebühren:</span>
                <span>
                  {" "}
                  {(blockingFee * (1 + vat / 100)).toFixed(2).replace(".", ",")}€/min (max{" "}
                  {blockingFeeMax}€)
                </span>
              </div>
            )}
            {blockingFee != 0 && (
              <div className={"flex flex-row gap-1 text-black text-opacity-50"}>
                <span className={"font-bold"}>Blockiergebühren ab:</span>
                <span>{blockingFeeBeginAtMin} Minuten</span>
              </div>
            )}

            <div className={"flex flex-row gap-1 text-black text-opacity-50"}>
              <span className={"font-bold"}>Abrechnung:</span>
              <span>{internal ? "keine" : "monatliche Rechnung: Lastschrift"}</span>
            </div>
          </div>
        </div>

        {interactive && (
          <>
            <div className="align-center flex min-h-6 justify-center px-4 py-2">
              <input
                id={tarifId}
                disabled={!optional}
                checked={checked}
                className="relative float-left mr-3 h-5 w-5 cursor-pointer appearance-none rounded-1.4 border border-solid border-slate-150 bg-white bg-contain bg-center bg-no-repeat align-top text-base transition-all duration-250 ease-soft after:absolute after:flex after:h-full after:w-full after:items-center after:justify-center after:text-xxs after:text-white after:opacity-0 after:transition-all after:duration-250 after:ease-soft-in-out after:content-['\2713'] checked:bg-primary  checked:after:opacity-100"
                type="checkbox"
                onChange={(value) => {
                  setChecked(value.target.checked);
                  onCheck ? onCheck(value) : "";
                }}
              />
              <label
                htmlFor={`checkbox-${title.split(" ")[0]}`}
                className="cursor-pointer select-none text-slate-700"
              >
                {optional ? "Auswählen (optional)" : "ausgewählt*"}
              </label>
            </div>
            {!optional && (
              <span className={"px-4 text-black text-opacity-50"}>
                *Standard-Tarif kann nicht abgewählt werden
              </span>
            )}
          </>
        )}

        <div className={"flex w-full flex-row justify-end px-4 text-sm text-black text-opacity-50"}>
          {vat > 0 ? <span>Preise inklusive Mwst.</span> : <span>Preise zzgl. Mwst.</span>}
        </div>
      </div>
    </div>
  );
};
export default TarifCard;
