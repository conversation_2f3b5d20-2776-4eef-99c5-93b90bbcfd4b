import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixUserGroupTarifLimits() {
  console.log('🚀 Starting UserGroup tarif limit fix...');

  try {
    // Find all UserGroups with their tarif assignments
    const userGroups = await prisma.userGroup.findMany({
      include: {
        companyTarifs: {
          include: {
            tarif: {
              select: {
                id: true,
                name: true,
                currentType: true,
                validFrom: true,
                validTo: true,
              },
            },
          },
        },
      },
    });

    console.log(`📊 Found ${userGroups.length} UserGroups to check`);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let fixedUserGroups = 0;
    let removedAssignments = 0;

    for (const userGroup of userGroups) {
      console.log(`\n🏢 Checking UserGroup: ${userGroup.name} (${userGroup.id})`);
      
      // Get valid tarifs only
      const validTarifs = userGroup.companyTarifs.filter(ct => 
        ct.tarif.validFrom <= today && ct.tarif.validTo >= today
      );

      const acTarifs = validTarifs.filter(ct => ct.tarif.currentType === 'AC');
      const dcTarifs = validTarifs.filter(ct => ct.tarif.currentType === 'DC');

      console.log(`  📈 Valid tarifs: ${validTarifs.length} (AC: ${acTarifs.length}, DC: ${dcTarifs.length})`);

      let needsFix = false;
      const tarifsToRemove = [];

      // Check AC tarifs - keep only the first one if multiple
      if (acTarifs.length > 1) {
        console.log(`  ⚠️  Multiple AC tarifs found (${acTarifs.length}), keeping only the first one`);
        const tarifsToRemoveAC = acTarifs.slice(1); // Remove all except first
        tarifsToRemove.push(...tarifsToRemoveAC);
        needsFix = true;
        
        console.log(`    Keeping: ${acTarifs[0].tarif.name}`);
        tarifsToRemoveAC.forEach(ct => {
          console.log(`    Removing: ${ct.tarif.name}`);
        });
      }

      // Check DC tarifs - keep only the first one if multiple
      if (dcTarifs.length > 1) {
        console.log(`  ⚠️  Multiple DC tarifs found (${dcTarifs.length}), keeping only the first one`);
        const tarifsToRemoveDC = dcTarifs.slice(1); // Remove all except first
        tarifsToRemove.push(...tarifsToRemoveDC);
        needsFix = true;
        
        console.log(`    Keeping: ${dcTarifs[0].tarif.name}`);
        tarifsToRemoveDC.forEach(ct => {
          console.log(`    Removing: ${ct.tarif.name}`);
        });
      }

      if (needsFix) {
        // Remove excess tarif assignments
        const tarifIdsToRemove = tarifsToRemove.map(ct => ct.tarifId);
        
        await prisma.companyTarifOnUserGroup.deleteMany({
          where: {
            userGroupId: userGroup.id,
            tarifId: { in: tarifIdsToRemove },
          },
        });

        fixedUserGroups++;
        removedAssignments += tarifIdsToRemove.length;
        console.log(`  ✅ Fixed UserGroup: removed ${tarifIdsToRemove.length} excess tarif assignments`);
      } else {
        console.log(`  ✅ UserGroup is compliant (max 1 AC, max 1 DC)`);
      }
    }

    console.log('\n🎉 UserGroup tarif limit fix completed!');
    console.log(`📈 Summary:`);
    console.log(`  - UserGroups checked: ${userGroups.length}`);
    console.log(`  - UserGroups fixed: ${fixedUserGroups}`);
    console.log(`  - Tarif assignments removed: ${removedAssignments}`);

    if (fixedUserGroups === 0) {
      console.log(`  🎯 All UserGroups were already compliant!`);
    }

  } catch (error) {
    console.error('❌ Error during fix:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixUserGroupTarifLimits()
  .then(() => {
    console.log('✅ Fix script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Fix script failed:', error);
    process.exit(1);
  });
